<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\Framework;

use PHPUnit\Framework\Attributes\CoversMethod;
use PHPUnit\Framework\Attributes\Small;
use PHPUnit\Framework\Attributes\TestDox;

#[CoversMethod(Assert::class, 'markTestIncomplete')]
#[TestDox('markTestIncomplete()')]
#[Small]
final class markTestIncompleteTest extends TestCase
{
    public function testMarksTestAsIncomplete(): void
    {
        $message = 'message';

        $this->expectException(IncompleteTestError::class);
        $this->expectExceptionMessage($message);

        $this->markTestIncomplete($message);
    }
}
