--TEST--
https://github.com/se<PERSON><PERSON><PERSON><PERSON>/phpunit/issues/6115
--FILE--
<?php declare(strict_types=1);
$_SERVER['argv'][] = '--do-not-cache-result';
$_SERVER['argv'][] = '--no-configuration';
$_SERVER['argv'][] = '--testdox';
$_SERVER['argv'][] = __DIR__ . '/6115/Issue6115Test.php';

require_once __DIR__ . '/../../bootstrap.php';

(new PHPUnit\TextUI\Application)->run($_SERVER['argv']);
--EXPECTF--
PHPUnit %s by <PERSON> and contributors.

Runtime: %s

.                                                                   1 / 1 (100%)

Time: %s, Memory: %s

Issue6115 (PHPUnit\TestFixture\Issue6115\Issue6115)
 ✔ 1

OK (1 test, 1 assertion)
