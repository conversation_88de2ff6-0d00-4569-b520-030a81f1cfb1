<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\TestFixture\Issue5908;

use Exception;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

final class Issue5908Test extends TestCase
{
    public static function provider(): array
    {
        throw new Exception('message');
    }

    #[DataProvider('provider')]
    public function testOne(int $value): void
    {
    }
}
